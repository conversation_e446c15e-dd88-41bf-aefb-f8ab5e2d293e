$font-mono: ui-monospace, 'Fira Code', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
$code-font-size: 13px;

@mixin not-inside-actions {
  &:not(:has(:global(.actions)), :global(.actions *)) {
    @content;
  }
}

.MarkdownContent {
  line-height: 1.6;
  color: var(--bolt-elements-textPrimary);

  > *:not(:last-child) {
    margin-block-end: 16px;
  }

  :global(.artifact) {
    margin: 1.5em 0;
  }

  :is(h1, h2, h3, h4, h5, h6) {
    @include not-inside-actions {
      margin-block-start: 24px;
      margin-block-end: 16px;
      font-weight: 600;
      line-height: 1.25;
      color: var(--bolt-elements-textPrimary);
    }
  }

  h1 {
    font-size: 2em;
    border-bottom: 1px solid var(--bolt-elements-borderColor);
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.5em;
    border-bottom: 1px solid var(--bolt-elements-borderColor);
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.25em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.875em;
  }

  h6 {
    font-size: 0.85em;
    color: #6a737d;
  }

  p {
    white-space: pre-wrap;

    &:not(:last-of-type) {
      margin-block-start: 0;
      margin-block-end: 16px;
    }
  }

  a {
    color: var(--bolt-elements-messages-linkColor);
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  :not(pre) > code {
    font-family: $font-mono;
    font-size: $code-font-size;

    @include not-inside-actions {
      border-radius: 6px;
      padding: 0.2em 0.4em;
      background-color: var(--bolt-elements-messages-inlineCode-background);
      color: var(--bolt-elements-messages-inlineCode-text);
    }
  }

  pre {
    padding: 20px 16px;
    border-radius: 6px;
  }

  pre:has(> code) {
    font-family: $font-mono;
    font-size: $code-font-size;
    background: transparent;
    overflow-x: auto;
    min-width: 0;
  }

  blockquote {
    margin: 0;
    padding: 0 1em;
    color: var(--bolt-elements-textTertiary);
    border-left: 0.25em solid var(--bolt-elements-borderColor);
  }

  :is(ul, ol) {
    @include not-inside-actions {
      padding-left: 2em;
      margin-block-start: 0;
      margin-block-end: 16px;
    }
  }

  ul {
    @include not-inside-actions {
      list-style-type: disc;
    }
  }

  ol {
    @include not-inside-actions {
      list-style-type: decimal;
    }
  }

  li {
    @include not-inside-actions {
      & + li {
        margin-block-start: 8px;
      }

      > *:not(:last-child) {
        margin-block-end: 16px;
      }
    }
  }

  img {
    max-width: 100%;
    box-sizing: border-box;
  }

  hr {
    height: 0.25em;
    padding: 0;
    margin: 24px 0;
    background-color: var(--bolt-elements-borderColor);
    border: 0;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin-block-end: 16px;

    :is(th, td) {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }

    tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
  }
}
